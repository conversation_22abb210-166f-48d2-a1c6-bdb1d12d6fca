package com.energyfuture.ndtp.entity.faultSet;

import java.util.List;

/**
 * 生成故障集请求实体类
 */
public class GenerateFaultSetRequest {
    
    /**
     * 故障类型：单永N-1故障、三永N-1故障、三永N-2故障
     */
    private String faultType;
    
    /**
     * 线路列表
     */
    private List<LineInfo> lines;
    
    public GenerateFaultSetRequest() {
    }
    
    public GenerateFaultSetRequest(String faultType, List<LineInfo> lines) {
        this.faultType = faultType;
        this.lines = lines;
    }
    
    public String getFaultType() {
        return faultType;
    }
    
    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }
    
    public List<LineInfo> getLines() {
        return lines;
    }
    
    public void setLines(List<LineInfo> lines) {
        this.lines = lines;
    }
    
    @Override
    public String toString() {
        return "GenerateFaultSetRequest{" +
                "faultType='" + faultType + '\'' +
                ", lines=" + lines +
                '}';
    }
}
