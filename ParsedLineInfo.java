/**
 * 解析后的线路信息实体类
 * 包含从ID中解析出的起点、终点、回路号信息
 */
public class ParsedLineInfo {
    
    /**
     * 起点节点名称，如：皖滨江__230.
     */
    private String startNode;
    
    /**
     * 终点节点名称，如：皖新江__230.
     */
    private String endNode;
    
    /**
     * 回路号，如：1
     */
    private String circuit;
    
    public ParsedLineInfo() {
    }
    
    public ParsedLineInfo(String startNode, String endNode, String circuit) {
        this.startNode = startNode;
        this.endNode = endNode;
        this.circuit = circuit;
    }
    
    public String getStartNode() {
        return startNode;
    }
    
    public void setStartNode(String startNode) {
        this.startNode = startNode;
    }
    
    public String getEndNode() {
        return endNode;
    }
    
    public void setEndNode(String endNode) {
        this.endNode = endNode;
    }
    
    public String getCircuit() {
        return circuit;
    }
    
    public void setCircuit(String circuit) {
        this.circuit = circuit;
    }
    
    @Override
    public String toString() {
        return "ParsedLineInfo{" +
                "startNode='" + startNode + '\'' +
                ", endNode='" + endNode + '\'' +
                ", circuit='" + circuit + '\'' +
                '}';
    }
}
