package com.energyfuture.ndtp.service.faultSet.impl;

import com.energyfuture.ndtp.entity.faultSet.ParsedLineInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.util.Arrays;

/**
 * 线路ID解析器
 * 负责从完整的线路ID中解析出起点、终点、回路号信息
 */
public class LineIdParser {
    
    private static final Logger log = LoggerFactory.getLogger(LineIdParser.class);
    private static final Charset GBK_CHARSET = Charset.forName("GBK");
    
    /**
     * 解析线路ID
     * @param id 完整的线路ID，如：皖滨江__230.0皖新江__230.01
     * @return 解析后的线路信息
     */
    public ParsedLineInfo parse(String id) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("线路ID不能为空");
        }
        
        try {
            byte[] idBytes = id.getBytes(GBK_CHARSET);
            
            if (idBytes.length < 25) { // 最小长度检查
                throw new IllegalArgumentException("线路ID格式不正确，长度过短: " + id);
            }
            
            // 1. 提取起点节点（前12字节）
            byte[] startNodeBytes = Arrays.copyOfRange(idBytes, 0, 12);
            String startNode = new String(startNodeBytes, GBK_CHARSET);
            
            // 2. 判断电压等级类型（检查第12字节，即起点节点的最后一个字节）
            boolean isThreeDigitVoltage = (idBytes[11] == '.');
            int skipBytes = isThreeDigitVoltage ? 1 : 2;
            
            log.debug("解析ID: {}, 起点节点: {}, 三位数电压等级: {}, 跳过字节数: {}", 
                     id, startNode, isThreeDigitVoltage, skipBytes);
            
            // 3. 提取终点节点
            int endNodeStart = 12 + skipBytes;
            if (endNodeStart + 12 > idBytes.length) {
                throw new IllegalArgumentException("线路ID格式不正确，无法提取终点节点: " + id);
            }
            
            byte[] endNodeBytes = Arrays.copyOfRange(idBytes, endNodeStart, endNodeStart + 12);
            String endNode = new String(endNodeBytes, GBK_CHARSET);
            
            // 4. 提取回路号
            int circuitStart = endNodeStart + 12 + skipBytes;
            if (circuitStart >= idBytes.length) {
                throw new IllegalArgumentException("线路ID格式不正确，无法提取回路号: " + id);
            }
            
            byte[] circuitBytes = Arrays.copyOfRange(idBytes, circuitStart, circuitStart + 1);
            String circuit = new String(circuitBytes, GBK_CHARSET);
            
            // 5. 格式化节点名称（去掉末尾的.0，保留.）
            String formattedStartNode = formatNodeName(startNode, isThreeDigitVoltage);
            String formattedEndNode = formatNodeName(endNode, isThreeDigitVoltage);
            
            log.debug("解析结果 - 起点: {}, 终点: {}, 回路: {}", 
                     formattedStartNode, formattedEndNode, circuit);
            
            return new ParsedLineInfo(formattedStartNode, formattedEndNode, circuit);
            
        } catch (Exception e) {
            log.error("解析线路ID失败: {}", id, e);
            throw new RuntimeException("解析线路ID失败: " + id, e);
        }
    }
    
    /**
     * 格式化节点名称
     * @param nodeName 原始节点名称
     * @param isThreeDigitVoltage 是否为三位数电压等级
     * @return 格式化后的节点名称
     */
    private String formatNodeName(String nodeName, boolean isThreeDigitVoltage) {
        if (isThreeDigitVoltage) {
            // 三位数电压等级：保持原样，已经是 皖滨江__230. 格式
            return nodeName;
        } else {
            // 四位数电压等级：已经是 国古泉_21200 格式，保持原样
            return nodeName;
        }
    }
}
