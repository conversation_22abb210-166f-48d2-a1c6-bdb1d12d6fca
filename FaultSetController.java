package com.energyfuture.ndtp.controller;


import com.energyfuture.ndtp.entity.faultSet.FaultSetGroupDTO;
import com.energyfuture.ndtp.entity.faultSet.GenerateFaultSetRequest;
import com.energyfuture.ndtp.service.faultSet.FaultSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("faultSet")
public class FaultSetController {

    @Autowired
    private FaultSetService faultSetService;


    @PostMapping("/generate")
    public Map<String, Object> generateFaultSet(@RequestBody GenerateFaultSetRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String faultSetText = faultSetService.generateFaultSetText(request);
            Map<String, String> data = new HashMap<>();
            data.put("faultSetText", faultSetText);

            response.put("code", 0);
            response.put("data", data);
            response.put("msg", "生成成功");
        } catch (Exception e) {
            // 捕获其他未知异常
            response.put("code", 500);
            response.put("data", null);
            response.put("msg", e.getMessage());
        }
        return response;
    }
}
