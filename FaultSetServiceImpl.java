package com.energyfuture.ndtp.service.faultSet.impl;


import com.energyfuture.ndtp.entity.faultSet.FaultSetGroupDTO;
import com.energyfuture.ndtp.entity.faultSet.FaultSetItemDTO;
import com.energyfuture.ndtp.entity.faultSet.GenerateFaultSetRequest;
import com.energyfuture.ndtp.entity.faultSet.LineInfo;
import com.energyfuture.ndtp.service.faultSet.FaultSetService;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class FaultSetServiceImpl implements FaultSetService {

    private static final Logger log = LoggerFactory.getLogger(FaultSetServiceImpl.class);

    // 必须使用 GBK 编码
    private static final Charset GBK_CHARSET = Charset.forName("GBK");

    @Value("${project.config.faultSetPath}")
    private String faultSetPath;

    
    @Override
    public String generateFaultSetText(GenerateFaultSetRequest request) {
        if (request == null || request.getFaultType() == null || request.getLines() == null || request.getLines().isEmpty()) {
            throw new IllegalArgumentException("请求参数无效，故障类型和线路列表不能为空。");
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            for (LineInfo line : request.getLines()) {
                switch (request.getFaultType()) {
                    case "单永N-1故障":
                        outputStream.write(buildFltForSingleN1(line));
                        break;
                    case "三永N-1故障":
                        outputStream.write(buildLsForTripleN1(line));
                        break;
                    case "三永N-2故障":
                        outputStream.write(buildLsForTripleN2(line));
                        break;
                    default:
                        String unsupportedText = String.format("# 未知的故障类型: %s%n", request.getFaultType());
                        outputStream.write(unsupportedText.getBytes(GBK_CHARSET));
                        break;
                }
            }
            // 使用兼容所有JDK版本的方式，将字节流以 GBK 编码转换为最终字符串
            return new String(outputStream.toByteArray(), GBK_CHARSET);
        } catch (IOException e) {
            log.error("构建故障集文本时发生内存流错误", e);
            throw new RuntimeException("生成文本时发生内部错误", e);
        }
    }

    // --- 字节操作核心辅助工具 ---

    /**
     * 创建一个固定字节宽度的字段。
     * 内容将左对齐，不足部分用空格填充，超出部分将被截断。
     * @param content 要填充的字符串内容
     * @param targetByteWidth 字段的目标字节宽度
     * @return 符合固定宽度的字节数组
     */
    private byte[] createField(String content, int targetByteWidth) throws UnsupportedEncodingException {
        // 1. 创建一个填满空格的字节数组作为底板
        byte[] field = new byte[targetByteWidth];
        Arrays.fill(field, (byte) ' ');

        // 2. 将内容字符串转换为 GBK 字节
        byte[] contentBytes = content.getBytes(GBK_CHARSET);

        // 3. 计算实际要复制的字节数（取内容字节和字段宽度的较小者）
        int lengthToCopy = Math.min(contentBytes.length, targetByteWidth);

        // 4. 将内容字节复制到底板的起始位置
        System.arraycopy(contentBytes, 0, field, 0, lengthToCopy);

        return field;
    }

    /**
     * 为卡片字节数组追加一个换行符 (Windows: \r\n, Linux/Mac: \n)
     */
    private byte[] appendNewLine(byte[] card) throws IOException {
        byte[] newline = System.lineSeparator().getBytes(GBK_CHARSET);
        ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
        tempStream.write(card);
        tempStream.write(newline);
        return tempStream.toByteArray();
    }


    // --- 私有卡片生成器 (使用新的 createField 方法) ---

    private byte[] buildFltForSingleN1(LineInfo line) throws IOException {
        // FLT 皖滨江__230. 皖新江__230.1 3  1 1 10.05.0 5.0             35.035.040.040.0
        // 根据示例目测格式，请根据精确规范调整宽度
        // FIELD        WIDTH (bytes)
        // -------------------------
        // "FLT "       4
        // startNode    20
        // endNode      20
        // circuit      2
        // "3"          2
        // "1"          2
        // "1"          2
        // "10.0"       5
        // "5.0"        4
        // "5.0"        24 (包含13个空格)
        // "35.0..."    ...
        // 为了确保完整性，我们将后半部分作为一个整体
        String fixedParams = "3  1 1 10.05.0 5.0             35.035.040.040.0";

        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            stream.write(createField("FLT", 4)); // "FLT "
            stream.write(createField(line.getStartNode(), 20));
            stream.write(createField(line.getEndNode(), 20));
            stream.write(createField(line.getCircuit(), 2));
            stream.write(createField(fixedParams, 50)); // 给予足够的宽度

            return appendNewLine(stream.toByteArray());
        }
    }

    private byte[] buildLsForTripleN1(LineInfo line) throws IOException {
        // LS  皖滨江__230.  皖新江__230. 1    1    10.0
        // LS -皖滨江__230. -皖新江__230. 1   -1    15.0
        // FIELD        WIDTH (bytes)
        // -------------------------
        // "LS" / "LS-" 3
        // startNode    20
        // endNode      20
        // circuit      5
        // value2       5
        // value3       ...
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            // 第一行
            stream.write(createField("LS", 2));
            stream.write(createField(line.getStartNode(), 20));
            stream.write(createField(line.getEndNode(), 20));
            stream.write(createField(line.getCircuit(), 5));
            stream.write(createField("1", 5));
            stream.write(createField("10.0", 10));
            stream.write(System.lineSeparator().getBytes(GBK_CHARSET));

            // 第二行
            stream.write(createField("LS", 2));
            stream.write(createField("-" + line.getStartNode(), 20));
            stream.write(createField("-" + line.getEndNode(), 20));
            stream.write(createField(line.getCircuit(), 5));
            stream.write(createField("-1", 5));
            stream.write(createField("15.0", 10));
            stream.write(System.lineSeparator().getBytes(GBK_CHARSET));

            return stream.toByteArray();
        }
    }

    private byte[] buildLsForTripleN2(LineInfo line) throws IOException {
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            // 前两行与 N-1 相同
            stream.write(buildLsForTripleN1(line));

            // 第三行
            stream.write(createField("LS", 2));
            stream.write(createField("-" + line.getEndNode(), 20));
            stream.write(createField("-" + line.getStartNode(), 20));
            stream.write(createField("2", 5));
            stream.write(createField("-1", 5));
            stream.write(createField("15.0", 10));
            stream.write(System.lineSeparator().getBytes(GBK_CHARSET));

            return stream.toByteArray();
        }
    }


    
}
