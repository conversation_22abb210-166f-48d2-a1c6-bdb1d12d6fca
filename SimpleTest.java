/**
 * 简单测试故障集生成功能
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        try {
            // 创建服务实例
            SimpleFaultSetService service = new SimpleFaultSetService();
            
            // 测试数据1：三位数电压等级
            String lineId1 = "皖滨江__230.0皖新江__230.01";
            
            System.out.println("=== 测试1：三位数电压等级 - 单永N-1故障 ===");
            System.out.println("输入ID: " + lineId1);
            String result1 = service.generateFaultSetText("单永N-1故障", lineId1);
            System.out.println("生成结果:");
            System.out.println(result1);
            System.out.println();
            
            // 测试数据2：四位数电压等级
            String lineId2 = "国古泉_21200.0国昌吉__1200.03";
            
            System.out.println("=== 测试2：四位数电压等级 - 三永N-1故障 ===");
            System.out.println("输入ID: " + lineId2);
            String result2 = service.generateFaultSetText("三永N-1故障", lineId2);
            System.out.println("生成结果:");
            System.out.println(result2);
            System.out.println();
            
            // 测试数据3：三永N-2故障
            System.out.println("=== 测试3：三位数电压等级 - 三永N-2故障 ===");
            System.out.println("输入ID: " + lineId1);
            String result3 = service.generateFaultSetText("三永N-2故障", lineId1);
            System.out.println("生成结果:");
            System.out.println(result3);
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
