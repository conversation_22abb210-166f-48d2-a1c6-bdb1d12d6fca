import com.energyfuture.ndtp.entity.faultSet.GenerateFaultSetRequest;
import com.energyfuture.ndtp.entity.faultSet.LineInfo;
import com.energyfuture.ndtp.service.faultSet.impl.FaultSetServiceImpl;

import java.util.Arrays;

/**
 * 测试故障集生成功能
 */
public class TestFaultSetGeneration {
    
    public static void main(String[] args) {
        try {
            // 创建服务实例
            FaultSetServiceImpl service = new FaultSetServiceImpl();
            
            // 测试数据1：三位数电压等级
            LineInfo line1 = new LineInfo("皖滨江__230.0皖新江__230.01");
            GenerateFaultSetRequest request1 = new GenerateFaultSetRequest("单永N-1故障", Arrays.asList(line1));
            
            System.out.println("=== 测试1：三位数电压等级 - 单永N-1故障 ===");
            System.out.println("输入ID: " + line1.getId());
            String result1 = service.generateFaultSetText(request1);
            System.out.println("生成结果:");
            System.out.println(result1);
            System.out.println();
            
            // 测试数据2：四位数电压等级
            LineInfo line2 = new LineInfo("国古泉_21200.0国昌吉__1200.03");
            GenerateFaultSetRequest request2 = new GenerateFaultSetRequest("三永N-1故障", Arrays.asList(line2));
            
            System.out.println("=== 测试2：四位数电压等级 - 三永N-1故障 ===");
            System.out.println("输入ID: " + line2.getId());
            String result2 = service.generateFaultSetText(request2);
            System.out.println("生成结果:");
            System.out.println(result2);
            System.out.println();
            
            // 测试数据3：三永N-2故障
            GenerateFaultSetRequest request3 = new GenerateFaultSetRequest("三永N-2故障", Arrays.asList(line1));
            
            System.out.println("=== 测试3：三位数电压等级 - 三永N-2故障 ===");
            System.out.println("输入ID: " + line1.getId());
            String result3 = service.generateFaultSetText(request3);
            System.out.println("生成结果:");
            System.out.println(result3);
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
